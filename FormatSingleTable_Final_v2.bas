Sub FormatSingleTable_Final_v2()
    ' 功能：一键美化Word表格，包括格式、对齐、边框和清理
    ' 版本：5.0 (最终稳定版)

    ' --- 初始化 ---
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    On Error Resume Next

    Dim mytable As Table
    Dim rowNum As Long, colNum As Long
    Dim cellText As String

    ' --- 检查环境 ---
    If Selection.Information(wdWithInTable) Then
        Set mytable = Selection.Tables(1)
    Else
        MsgBox "请将光标放在需要格式化的表格中。", vbInformation, "操作提示"
        Exit Sub
    End If

    ' === 第一部分：设置表格整体格式 ===
    With mytable
        .TopPadding = PixelsToPoints(2, True)
        .BottomPadding = PixelsToPoints(2, True)
        .LeftPadding = PixelsToPoints(7, True)
        .RightPadding = PixelsToPoints(10, True)
        
        With .Range.Font
            .NameFarEast = "宋体"
            .NameAscii = "Times New Roman"
            .Size = 11
            .Spacing = 0
            .Scaling = 100
        End With
        
        For rowNum = 1 To .Rows.Count
            .Rows(rowNum).HeightRule = wdRowHeightAtLeast
            .Rows(rowNum).Height = CentimetersToPoints(0.7)
        Next rowNum
    End With

    ' === 第二部分：逐一处理单元格 ===
    For rowNum = 1 To mytable.Rows.Count
        For colNum = 1 To mytable.Columns.Count
            With mytable.Cell(rowNum, colNum)
                ' 1. 清理单元格文本（删除所有空格、Tab、回车、换行等）
                cellText = .Range.Text
                If Len(cellText) > 2 Then
                    cellText = Left(cellText, Len(cellText) - 2)
                End If
                cellText = Replace(cellText, Chr(160), "") ' 不间断空格
                cellText = Replace(cellText, "　", "") ' 全角空格
                cellText = Replace(cellText, vbTab, "")
                cellText = Replace(cellText, vbCr, "")
                cellText = Replace(cellText, vbLf, "")
                cellText = Replace(cellText, Chr(11), "")
                cellText = Replace(cellText, " ", "") ' 半角空格
                cellText = Trim(cellText)
                ' 再次彻底删除所有空格
                Do While InStr(cellText, " ") > 0
                    cellText = Replace(cellText, " ", "")
                Loop
                .Range.Text = cellText

                ' 2. 设置单元格基础格式
                With .Range.ParagraphFormat
                    .SpaceBefore = 0
                    .SpaceAfter = 0
                    .FirstLineIndent = 0
                    .LeftIndent = 0
                    .RightIndent = 0
                    .LineSpacingRule = wdLineSpaceExactly
                    .LineSpacing = 16
                End With
                .VerticalAlignment = wdCellAlignVerticalCenter

                ' 3. 设置对齐方式（重点：第一列特殊处理）
                If colNum = 1 Then
                    If rowNum = 1 Then
                        ' 第一列第一行（表头/合并单元格）居中
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                        .Range.Font.Bold = True
                    ElseIf rowNum = mytable.Rows.Count Or InStr(cellText, "单位") > 0 Or InStr(cellText, "元") > 0 Then
                        ' 第一列最后一行或内容为单位/元，居中
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf InStr(cellText, "%") > 0 Then
                        ' 百分数居中
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf IsNumeric(cellText) And Len(cellText) > 0 Then
                        ' 数字右对齐
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphRight
                    ElseIf Len(cellText) > 0 Then
                        ' 文字左对齐
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    Else
                        ' 其他情况居中
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    End If
                Else
                    ' 其他列按原有逻辑
                    If rowNum = 1 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                        .Range.Font.Bold = True
                    ElseIf InStr(cellText, "%") > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf IsNumeric(cellText) And Len(cellText) > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphRight
                    Else
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    End If
                End If

                ' 最后一行加粗
                If rowNum = mytable.Rows.Count Then
                    .Range.Font.Bold = True
                End If
            End With
        Next colNum
    Next rowNum

    ' === 第三部分：设置边框 ===
    With mytable.Borders
        .InsideLineStyle = wdLineStyleSingle
        .InsideLineWidth = wdLineWidth050pt
        .OutsideLineStyle = wdLineStyleDouble
        .OutsideLineWidth = wdLineWidth050pt
    End With

    ' === 第四部分：删除数字行下方的空行 ===
    For rowNum = mytable.Rows.Count To 2 Step -1
        Dim rowHasNumber As Boolean: rowHasNumber = False
        Dim rowBelowEmpty As Boolean: rowBelowEmpty = True
        
        For colNum = 1 To mytable.Columns.Count
            cellText = mytable.Cell(rowNum - 1, colNum).Range.Text
            If IsNumeric(Left(cellText, Len(cellText) - 2)) Then
                rowHasNumber = True
                Exit For
            End If
        Next colNum
        
        If rowHasNumber Then
            For colNum = 1 To mytable.Columns.Count
                cellText = mytable.Cell(rowNum, colNum).Range.Text
                If Len(Trim(Left(cellText, Len(cellText) - 2))) > 0 Then
                    rowBelowEmpty = False
                    Exit For
                End If
            Next colNum
            
            If rowBelowEmpty Then mytable.Rows(rowNum).Delete
        End If
    Next rowNum

    ' --- 结束 ---
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    MsgBox "表格格式化完成！", vbInformation, "操作成功"
End Sub

