# 仅供 PyInstaller 静态分析用
if False:
    import PyPDF2
    import pypdf
    import reportlab

# add_number.py —— 批量版：输入/输出文件夹
# -*- coding: utf-8 -*-
"""
用法
----
    python add_number.py 输入文件夹 输出文件夹 [可选: 统一前缀]

说明
----
* **输入文件夹**：遍历其中所有扩展名为 .pdf / .PDF 的文件。
* **输出文件夹**：若不存在会自动创建，生成的 PDF 与原文件同名覆盖。
* 每个 PDF 的页眉格式：
      <统一前缀或文件名前缀>-<页/总页>
  · 若提供了第 3 个参数，则对所有文件使用统一前缀；
  · 否则先取文件名中的数字串；若无数字则用文件名（去扩展名）。
* 仍保持：
  · 动态检测 PyPDF2 / pypdf，与 reportlab；
  · 缺库即弹窗提示安装；
  · 文字红色、字体 Helvetica-Bold 12pt，右上角距边 20pt。
"""

import os
import re
import sys
import importlib.util
from io import BytesIO
from typing import Optional

# ======= 动态导入 PyPDF2 / pypdf ===================================================
PdfReader = None
PdfWriter = None
for mod in ("PyPDF2", "pypdf"):
    try:
        m = importlib.import_module(mod)
        PdfReader = getattr(m, "PdfReader", None)
        PdfWriter = getattr(m, "PdfWriter", None)
        if PdfReader is not None and PdfWriter is not None:
            break
    except ImportError:
        continue

if PdfReader is None or PdfWriter is None:
    import tkinter.messagebox as mb
    mb.showerror("缺少依赖", "未找到 PyPDF2 或 pypdf 库，脚本无法运行。\n\n请执行:\n    pip install PyPDF2")
    sys.exit(1)

# ======= 动态导入 reportlab ========================================================
if importlib.util.find_spec("reportlab"):
    from reportlab.pdfgen import canvas
    from reportlab.lib.colors import Color
else:
    import tkinter.messagebox as mb
    mb.showerror("缺少依赖", "未找到 reportlab 库，脚本无法运行。\n\n请执行:\n    pip install reportlab")
    sys.exit(1)

# ======= 样式常量 ===================================================================
FONT_NAME = "Helvetica-Bold"
FONT_SIZE = 12
COLOR_RED = Color(1, 0, 0)
MARGIN_X = 20
MARGIN_Y = 20

# ======= 工具函数 ===================================================================
def extract_prefix(path: str) -> str:
    """先取文件名中的数字串；若无则用去扩展名的文件名"""
    base = os.path.splitext(os.path.basename(path))[0]
    m = re.search(r"(\d+)", base)
    return m.group(1) if m else base

def add_page_numbers(in_pdf: str, out_pdf: str, prefix: str) -> None:
    reader = PdfReader(in_pdf)
    writer = PdfWriter()
    total = len(reader.pages)

    for idx, page in enumerate(reader.pages, 1):
        w, h = float(page.mediabox.width), float(page.mediabox.height)
        txt_core = f"{idx}/{total}"
        txt_full = f"{prefix}-{txt_core}" if prefix else txt_core

        buf = BytesIO()
        c = canvas.Canvas(buf, pagesize=(w, h))
        c.setFont(FONT_NAME, FONT_SIZE)
        c.setFillColor(COLOR_RED)
        tw = c.stringWidth(txt_full, FONT_NAME, FONT_SIZE)
        c.drawString(w - tw - MARGIN_X, h - MARGIN_Y, txt_full)
        c.save(); buf.seek(0)

        overlay = PdfReader(buf).pages[0]
        page.merge_page(overlay)
        writer.add_page(page)

    with open(out_pdf, "wb") as f:
        writer.write(f)

# ======= 主流程（GUI 版：多选PDF文件）============================================================
if __name__ == "__main__":
    import tkinter as tk
    from tkinter import filedialog, simpledialog, messagebox

    root = tk.Tk()
    root.withdraw()

    pdf_paths = filedialog.askopenfilenames(title="请选择要编号的PDF文件", filetypes=[("PDF Files", "*.pdf")])
    if not pdf_paths:
        messagebox.showinfo("提示", "未选择PDF文件，程序已取消。")
        sys.exit(0)

    out_dir = filedialog.askdirectory(title="请选择输出文件夹（生成PDF保存位置）")
    if not out_dir:
        messagebox.showinfo("提示", "未选择输出文件夹，程序已取消。")
        sys.exit(0)

    os.makedirs(out_dir, exist_ok=True)

    success, fail = 0, 0
    for src in pdf_paths:
        fname = os.path.basename(src)
        dst = os.path.join(out_dir, fname)
        prefix = extract_prefix(fname)
        try:
            add_page_numbers(src, dst, prefix)
            success += 1
        except Exception as e:
            fail += 1
            messagebox.showerror("处理失败", f"文件: {fname}\n错误: {e}")

    messagebox.showinfo("处理完成", f"全部处理完成！\n成功: {success} 个\n失败: {fail} 个")
