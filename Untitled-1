Sub FormatSingleTable1(control As IRibbonControl) '专项报告单表格一键调整

Application.ScreenUpdating = False
Application.DisplayAlerts = False
On Error Resume Next

' 定义变量
Dim mytable As Table
Dim i As Long, j <PERSON> Long
Dim cellRange As Range
Dim hasPercentColumn() As Boolean ' 新增：记录哪些列包含百分号

' 检查是否在表格中
If Selection.Information(wdWithInTable) Then
    Set mytable = Selection.Tables(1)
Else
    MsgBox "请将光标放在表格中或选中表格后再运行此宏。"
    Exit Sub
End If

' 初始化百分号列数组
ReDim hasPercentColumn(1 To mytable.Columns.Count)

' === 第一部分：设置基础格式 ===
With mytable
    ' 设置表格整体格式
    .TopPadding = PixelsToPoints(2, True)
    .BottomPadding = PixelsToPoints(2, True)
    .LeftPadding = PixelsToPoints(7, True)
    .RightPadding = PixelsToPoints(10, True)
    
    ' 字体格式
    With .Range.Font
        .NameFarEast = "宋体"
        .NameAscii = "Times New Roman"
        .Size = 11
        .Spacing = 0
    End With
    
    ' 行高设置
    For i = 1 To .Rows.Count
        .Rows(i).HeightRule = wdRowHeightAtLeast
        .Rows(i).Height = CentimetersToPoints(0.7)
    Next i
    
    ' 统一设置所有单元格基础格式
    For i = 1 To .Rows.Count
        For j = 1 To .Columns.Count
            If Err.Number = 0 Then
                Set cellRange = .cell(i, j).Range
                With cellRange.ParagraphFormat
                    .SpaceBefore = 0
                    .SpaceAfter = 0
                    .FirstLineIndent = 0
                    .LeftIndent = 0
                    .RightIndent = 0
                    .LineSpacingRule = wdLineSpaceExactly
                    .LineSpacing = 16
                    .AutoAdjustRightIndent = False
                    .Alignment = wdAlignParagraphCenter ' 默认居中
                End With
                .cell(i, j).VerticalAlignment = wdCellAlignVerticalCenter
            End If
            Err.Clear
        Next j
    Next i
End With

' === 第一步：识别包含百分号的列 ===
With mytable
    For j = 1 To .Columns.Count
        On Error Resume Next
        ' 检查第一行（标题行）是否包含百分号
        Dim titleText As String
        titleText = Left(.cell(1, j).Range.Text, Len(.cell(1, j).Range.Text) - 2)
        If InStr(titleText, "%") > 0 Then
            hasPercentColumn(j) = True
        End If
        Err.Clear
    Next j
End With

' === 第二部分：逐一处理单元格 ===
    For rowNum = 1 To lastRow
        For colNum = 1 To mytable.Columns.Count
            On Error Resume Next
            With mytable.cell(rowNum, colNum)
                ' 1. 清理单元格文本，删除所有空格
                cellText = .Range.Text
                If Len(cellText) > 2 Then
                    cellText = Left(cellText, Len(cellText) - 2)
                End If
                cellText = Replace(cellText, Chr(160), "") ' 不间断空格
                cellText = Replace(cellText, "　", "") ' 全角空格
                cellText = Replace(cellText, " ", "") ' 半角空格
                cellText = Replace(cellText, vbTab, "")
                cellText = Replace(cellText, vbCr, "")
                cellText = Replace(cellText, vbLf, "")
                cellText = Trim(cellText)
                Do While InStr(cellText, " ") > 0
                    cellText = Replace(cellText, " ", "")
                Loop
                .Range.Text = cellText

                ' 2. 设置单元格基础格式
                With .Range.ParagraphFormat
                    .SpaceBefore = 0
                    .SpaceAfter = 0
                    .SpaceBeforeAuto = False
                    .SpaceAfterAuto = False
                    .FirstLineIndent = 0
                    .LeftIndent = 0
                    .RightIndent = 0
                    .LineSpacingRule = wdLineSpaceExactly
                    .LineSpacing = 16
                End With
                .VerticalAlignment = wdCellAlignVerticalCenter

                ' 3. 设置对齐方式
                If rowNum = 1 Then
                    ' 第一行全部居中
                    .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    .Range.Font.Bold = True
                ElseIf colNum = 1 Then
                    ' 第一列
                    If InStr(cellText, "%") > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf IsNumeric(cellText) And Len(cellText) > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphRight
                    ElseIf Len(cellText) > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    Else
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    End If
                    .Range.Font.Bold = False
                ElseIf colNum = 2 And rowNum > 1 Then
                    ' 第二列第二行开始
                    If InStr(cellText, "%") > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf IsNumeric(cellText) And Len(cellText) > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphRight
                    ElseIf Len(cellText) > 0 Then
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    Else
                        .Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    End If
                    .Range.Font.Bold = False
                Else
                    ' 其他列及行，默认靠左居中
                    .Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    .Range.Font.Bold = False
                End If
            End With
            On Error GoTo 0
        Next colNum
    Next rowNum

' === 第三部分：设置边框 ===
With mytable
    .Borders(wdBorderLeft).LineStyle = wdLineStyleNone
    .Borders(wdBorderRight).LineStyle = wdLineStyleNone
    
    With .Borders(wdBorderTop)
        .LineStyle = wdLineStyleDouble
        .LineWidth = wdLineWidth050pt
    End With
    
    With .Borders(wdBorderBottom)
        .LineStyle = wdLineStyleDouble
        .LineWidth = wdLineWidth050pt
    End With
    
    With .Borders(wdBorderHorizontal)
        .LineStyle = wdLineStyleSingle
        .LineWidth = wdLineWidth050pt
    End With
    
    With .Borders(wdBorderVertical)
        .LineStyle = wdLineStyleSingle
        .LineWidth = wdLineWidth050pt
    End With
    
    .AutoFitBehavior wdAutoFitWindow
    .PreferredWidthType = wdPreferredWidthPercent
    .PreferredWidth = 100
End With

' 恢复设置
Application.DisplayAlerts = True
Application.ScreenUpdating = True
MsgBox "表格格式化完成！"

End Sub