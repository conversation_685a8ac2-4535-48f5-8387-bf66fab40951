(['D:\\Documents\\Desktop\\Trae\\import_macro.py'],
 ['D:\\Documents\\Desktop\\Trae'],
 [],
 [('c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.9.0 (tags/v3.9.0:9cf6752, Oct  5 2020, 15:34:40) [MSC v.1927 64 bit '
 '(AMD64)]',
 [('pyi_rth_pywintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('import_macro',
   'D:\\Documents\\Desktop\\Trae\\import_macro.py',
   'PYSOURCE')],
 [('copy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\copy.py',
   'PYMODULE'),
  ('string',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\string.py',
   'PYMODULE'),
  ('tracemalloc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\struct.py',
   'PYMODULE'),
  ('fnmatch',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\fnmatch.py',
   'PYMODULE'),
  ('_py_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_py_abc.py',
   'PYMODULE'),
  ('typing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\contextlib.py',
   'PYMODULE'),
  ('tokenize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\tokenize.py',
   'PYMODULE'),
  ('argparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\textwrap.py',
   'PYMODULE'),
  ('shutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\shutil.py',
   'PYMODULE'),
  ('zipfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\calendar.py',
   'PYMODULE'),
  ('datetime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\selectors.py',
   'PYMODULE'),
  ('random',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('bisect',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\bisect.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\getopt.py',
   'PYMODULE'),
  ('quopri',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\csv.py',
   'PYMODULE'),
  ('threading',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\bz2.py',
   'PYMODULE'),
  ('gettext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\gettext.py',
   'PYMODULE'),
  ('token',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\token.py',
   'PYMODULE'),
  ('stringprep',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\stringprep.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('win32con',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('glob',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\glob.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pythoncom',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('__future__',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\__future__.py',
   'PYMODULE'),
  ('subprocess',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\signal.py',
   'PYMODULE')],
 [('python39.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\python39.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes39.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\pywin32_system32\\pywintypes39.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom39.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\pywin32_system32\\pythoncom39.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32event.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\win32event.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\Documents\\Desktop\\Trae\\build\\import_macro\\base_library.zip',
   'DATA')],
 [('sre_constants',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\sre_constants.py',
   'PYMODULE'),
  ('ntpath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\ntpath.py',
   'PYMODULE'),
  ('warnings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\warnings.py',
   'PYMODULE'),
  ('_weakrefset',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('locale',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\locale.py',
   'PYMODULE'),
  ('_bootlocale',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_bootlocale.py',
   'PYMODULE'),
  ('re',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\re.py',
   'PYMODULE'),
  ('posixpath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\posixpath.py',
   'PYMODULE'),
  ('enum',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\enum.py',
   'PYMODULE'),
  ('abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\abc.py',
   'PYMODULE'),
  ('operator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\operator.py',
   'PYMODULE'),
  ('sre_parse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\sre_parse.py',
   'PYMODULE'),
  ('copyreg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\copyreg.py',
   'PYMODULE'),
  ('traceback',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\traceback.py',
   'PYMODULE'),
  ('genericpath',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\genericpath.py',
   'PYMODULE'),
  ('functools',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\functools.py',
   'PYMODULE'),
  ('weakref',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\weakref.py',
   'PYMODULE'),
  ('keyword',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\keyword.py',
   'PYMODULE'),
  ('heapq',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\heapq.py',
   'PYMODULE'),
  ('collections.abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('stat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\stat.py',
   'PYMODULE'),
  ('_collections_abc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('io',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\io.py',
   'PYMODULE'),
  ('types',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\types.py',
   'PYMODULE'),
  ('linecache',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\linecache.py',
   'PYMODULE'),
  ('reprlib',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\reprlib.py',
   'PYMODULE'),
  ('sre_compile',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\sre_compile.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('codecs',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\codecs.py',
   'PYMODULE'),
  ('os',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\os.py',
   'PYMODULE')])
