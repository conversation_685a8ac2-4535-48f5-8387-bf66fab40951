import os
import sys
import win32com.client
import time

def import_vba_module(bas_path):
    word = win32com.client.Dispatch("Word.Application")
    word.Visible = False
    try:
        # 打开 Normal 模板
        normal_template = word.NormalTemplate
        vbproject = normal_template.VBProject

        # 检查是否已存在同名模块，存在则先删除
        for comp in vbproject.VBComponents:
            if comp.Name == "MyMacro":
                vbproject.VBComponents.Remove(comp)
                break

        # 导入 .bas 文件
        vbproject.VBComponents.Import(bas_path)
        normal_template.Save()
        print("宏已成功导入到 Normal.dotm！")
        time.sleep(2)
    except Exception as e:
        print('导入失败，请确保：\n1. Word 已关闭\n2. 已在Word选项-信任中心-宏设置中勾选“信任对VBA项目对象模型的访问”\n3. 以管理员身份运行本程序\n\n错误信息：', e)
        time.sleep(8)
    finally:
        word.Quit()

if __name__ == "__main__":
    bas_file = os.path.join(os.path.dirname(sys.argv[0]), "NewMacros.bas")
    if not os.path.exists(bas_file):
        print("未找到 NewMacros.bas 文件，请将其与本程序放在同一目录下。")
        time.sleep(3)
        sys.exit(1)
    import_vba_module(bas_file)