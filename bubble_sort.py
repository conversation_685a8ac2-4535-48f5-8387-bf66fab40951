def bubble_sort(arr):
    """
    对一个列表进行冒泡排序

    参数:
    arr (list): 需要排序的列表

    返回:
    list: 排序后的列表
    """
    n = len(arr)
    # 遍历所有数组元素
    for i in range(n):
        # Last i elements are already in place
        for j in range(0, n-i-1):
            # 遍历数组从 0 到 n-i-1
            # 如果找到的元素大于下一个元素，则交换
            if arr[j] > arr[j+1]:
                arr[j], arr[j+1] = arr[j+1], arr[j]
    return arr

# 示例
if __name__ == '__main__':
    sample_list = [64, 34, 25, 12, 22, 11, 90]
    print(f"原始列表: {sample_list}")
    sorted_list = bubble_sort(sample_list)
    print(f"排序后的列表: {sorted_list}")
