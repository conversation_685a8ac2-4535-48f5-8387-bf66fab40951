Sub FormatSingleTable(control As IRibbonControl) '单表-一键美颜（修复数字缩进问题）

Application.ScreenUpdating = False
Application.DisplayAlerts = False
On Error Resume Next

Dim mytable As Table
Dim i As Long, j <PERSON> Long
Dim cellText As String
Dim hasPercentColumn() As <PERSON>olean ' 新增：记录哪些列包含百分号

' 检查是否在表格中
If Selection.Information(wdWithInTable) Then
    Set mytable = Selection.Tables(1)
Else
    MsgBox "请将光标放在表格中或选中表格后再运行此宏。"
    Exit Sub
End If

' 初始化百分号列数组
ReDim hasPercentColumn(1 To mytable.Columns.Count)

' === 第一部分：设置基础格式 ===
With mytable
    ' 单元格边距
    .TopPadding = PixelsToPoints(2, True)
    .BottomPadding = PixelsToPoints(2, True)
    .LeftPadding = PixelsToPoints(7, True)
    .RightPadding = PixelsToPoints(10, True)
    
    ' 字体格式
    With .Range.Font
        .NameFarEast = "宋体"
        .NameAscii = "Times New Roman"
        .Size = 11
        .Spacing = 0
        .Scaling = 100
    End With
    
    ' 行高设置
    For i = 1 To .Rows.Count
        .Rows(i).HeightRule = wdRowHeightAtLeast
        .Rows(i).Height = CentimetersToPoints(0.7)
        Err.Clear
    Next i
End With

' === 第二部分：先整体设置第一行（表头）居中加粗 ===
With mytable.Rows(1).Range
    .ParagraphFormat.Alignment = wdAlignParagraphCenter
    .Font.Bold = True
End With

' === 第三部分：统一设置单元格格式和对齐 ===
For i = 1 To mytable.Rows.Count
    For j = 1 To mytable.Columns.Count
        ' 获取单元格文本
        cellText = mytable.Cell(i, j).Range.Text
        If Len(cellText) > 2 Then
            cellText = Left(cellText, Len(cellText) - 2)
        End If
        
        ' === 增强版空格清理 ===
        ' 清理各种类型的空白字符
        cellText = Replace(cellText, Chr(160), "")    ' 不间断空格
        cellText = Replace(cellText, Chr(8194), "")   ' En空格
        cellText = Replace(cellText, Chr(8195), "")   ' Em空格
        cellText = Replace(cellText, Chr(8201), "")   ' 细空格
        cellText = Replace(cellText, Chr(8202), "")   ' 发丝空格
        cellText = Replace(cellText, Chr(8203), "")   ' 零宽空格
        cellText = Replace(cellText, Chr(8204), "")   ' 零宽非连字符
        cellText = Replace(cellText, Chr(8205), "")   ' 零宽连字符
        cellText = Replace(cellText, Chr(8287), "")   ' 中等数学空格
        cellText = Replace(cellText, vbTab, "")       ' 制表符
        cellText = Replace(cellText, vbCrLf, "")      ' 回车换行
        cellText = Replace(cellText, vbCr, "")        ' 回车
        cellText = Replace(cellText, vbLf, "")        ' 换行
        cellText = Replace(cellText, Chr(11), "")     ' 垂直制表符
        cellText = Replace(cellText, Chr(12), "")     ' 换页符
        cellText = Replace(cellText, "　", "")        ' 中文全角空格
        
        ' 清理普通空格并处理连续空格
        cellText = Trim(cellText)
        Do While InStr(cellText, "  ") > 0
            cellText = Replace(cellText, "  ", " ")
        Loop
        
        ' 清理标点符号前后的多余空格
        cellText = Replace(cellText, " ，", "，")
        cellText = Replace(cellText, " 。", "。")
        cellText = Replace(cellText, " ；", "；")
        cellText = Replace(cellText, " ：", "：")
        cellText = Replace(cellText, " ？", "？")
        cellText = Replace(cellText, " ！", "！")
        cellText = Replace(cellText, " 、", "、")
        cellText = Replace(cellText, "， ", "，")
        cellText = Replace(cellText, "。 ", "。")
        cellText = Replace(cellText, "； ", "；")
        cellText = Replace(cellText, "： ", "：")
        cellText = Replace(cellText, "？ ", "？")
        cellText = Replace(cellText, "！ ", "！")
        cellText = Replace(cellText, "、 ", "、")
        
        ' 最终清理
        cellText = Trim(cellText)
        
        ' 将清理后的文本重新写入单元格
        mytable.Cell(i, j).Range.Text = cellText
        
        ' 设置单元格基本格式
        With mytable.Cell(i, j).Range.ParagraphFormat
            .SpaceBefore = 0
            .SpaceAfter = 0
            .FirstLineIndent = 0
            .LeftIndent = 0
            .RightIndent = 0
            .LineSpacingRule = wdLineSpaceExactly
            .LineSpacing = 16
            .AutoAdjustRightIndent = False
        End With
        mytable.Cell(i, j).VerticalAlignment = wdCellAlignVerticalCenter
        
        ' 第一列特殊处理
        If j = 1 Then
            If i = 1 Then
                ' 第一行表头始终居中
                mytable.Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
            ElseIf i = mytable.Rows.Count And (cellText = "元" Or cellText = "万元" Or cellText = "千元" Or InStr(LCase(cellText), "单位") > 0) Then
                ' 最后一行单位居中
                mytable.Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
            ElseIf InStr(cellText, "%") > 0 Then
                ' 百分号居中
                mytable.Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    If Not IsNumeric(cellText) And InStr(cellText, "%") = 0 Then
                        mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    Else
                        mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    End If
                Else
                    ' 空内容居中
                    mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                End If
            Else
                ' 其他列的处理
                If rowIndex = 1 Then
                    ' 第一行表头居中加粗
                    mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    mytable.cell(rowIndex, colIndex).Range.Font.Bold = True
                ElseIf colIndex = 2 Then
                    ' 第二列左对齐
                    mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                ElseIf colIndex = 3 Then
                    ' 第三列居中
                    mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                ElseIf colIndex = 4 Then
                    ' 第四列金额处理
                    If InStr(cellText, "%") > 0 Then
                        mytable.cell(rowIndex, colIndex).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter


' === 第三部分：设置边框 ===
With mytable
    .Borders(wdBorderLeft).LineStyle = wdLineStyleNone
    .Borders(wdBorderRight).LineStyle = wdLineStyleNone
    With .Borders(wdBorderTop)
        .LineStyle = wdLineStyleDouble
        .LineWidth = wdLineWidth050pt
    End With
    With .Borders(wdBorderBottom)
        .LineStyle = wdLineStyleDouble
        .LineWidth = wdLineWidth050pt
    End With
    With .Borders(wdBorderHorizontal)
        .LineStyle = wdLineStyleSingle
        .LineWidth = wdLineWidth050pt
    End With
    With .Borders(wdBorderVertical)
        .LineStyle = wdLineStyleSingle
        .LineWidth = wdLineWidth050pt
    End With

    ' 关键：根据内容动态分配宽度
    .AutoFitBehavior wdAutoFitContent
End With

' === 第四部分：删除数字行下方的空行 ===
With mytable
    For i = .Rows.Count To 2 Step -1
        Dim rowHasNumber As Boolean: rowHasNumber = False
        Dim rowBelowEmpty As Boolean: rowBelowEmpty = True
        
        ' 检查上一行是否有数字
        For j = 1 To .Columns.Count
            cellText = Replace(Replace(.cell(i - 1, j).Range.Text, Chr(13), ""), Chr(7), "")
            If IsNumeric(cellText) And Len(Trim(cellText)) > 0 Then
                rowHasNumber = True: Exit For
            End If
            Err.Clear
        Next j
        
        ' 如果上一行有数字，检查当前行是否为空
        If rowHasNumber Then
            For j = 1 To .Columns.Count
                cellText = Replace(Replace(.cell(i, j).Range.Text, Chr(13), ""), Chr(7), "")
                If Len(Trim(cellText)) > 0 Then
                    rowBelowEmpty = False: Exit For
                End If
                Err.Clear
            Next j
            
            ' 删除空行
            If rowBelowEmpty Then .Rows(i).Delete
        End If
        Err.Clear
    Next i
End With

Application.DisplayAlerts = True
Application.ScreenUpdating = True
MsgBox "表格格式化完成！"

End Sub