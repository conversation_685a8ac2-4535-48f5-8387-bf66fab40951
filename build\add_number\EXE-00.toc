('D:\\Documents\\Desktop\\Trae\\dist\\add_number.exe',
 <PERSON><PERSON><PERSON>,
 <PERSON><PERSON>e,
 False,
 'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 <PERSON>als<PERSON>,
 <PERSON>alse,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\Documents\\Desktop\\Trae\\build\\add_number\\add_number.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('add_number', 'D:\\Documents\\Desktop\\Trae\\add_number.py', 'PYSOURCE'),
  ('python39.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\python39.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PIL\\_webp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PIL\\_imagingtk.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PIL\\_avif.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PIL\\_imagingcms.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PIL\\_imagingmath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PIL\\_imaging.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('tk86t.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tk_data\\tclIndex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tk_data\\button.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.14.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8\\8.4\\platform-1.0.14.tm',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tk_data\\images\\README',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tk_data\\console.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.0.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8\\8.6\\http-2.9.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.0.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8\\8.5\\tcltest-2.5.0.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tk_data\\text.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tk_data\\license.terms',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('base_library.zip',
   'D:\\Documents\\Desktop\\Trae\\build\\add_number\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1752165839,
 [('runw.exe',
   'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'c:\\users\\<USER>\\appdata\\local\\programs\\python\\python39\\python39.dll')
